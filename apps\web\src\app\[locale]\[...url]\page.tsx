import { Metadata } from 'next'
import { headers } from 'next/headers'
import { notFound } from 'next/navigation'
import {
  extractFileName,
  getCategoriesQuery,
  getCmsPageQuery,
  getProductsQuery,
  QUID_KEY,
  RATE_LIMIT,
  resolveUrlQuery,
  TBasePageProps,
  validateHttpStatus,
} from '@ninebot/core'

import { EmptyData } from '@/components/common'
import { redirect } from '@/i18n/navigation'
import { TLocales } from '@/i18n/type'

import CategoryPage from './_pages/category'
import CmsPage from './_pages/cms'
import ProductPage from './_pages/product'

type TResolveUrlPageProps = TBasePageProps<{ url: string[] }, void, TLocales>

/**
 * 生成元数据
 */
export async function generateMetadata({ params }: TResolveUrlPageProps): Promise<Metadata> {
  const { url: segments, locale } = params

  if (!segments.length) {
    return {}
  }

  const headersList = headers()
  const host = headersList.get('host')

  // TODO: 检查传入的 URL 安全性
  const safeUrl = decodeURIComponent(segments.join('/'))
  const fullUrl = `https://${host}/${safeUrl}`
  const quid = headersList.get(QUID_KEY.toUpperCase()) || ''

  const response = await resolveUrlQuery(
    {
      url: safeUrl,
    },
    {
      headers: {
        [QUID_KEY.toUpperCase()]: quid,
      },
    },
  )

  // 检查请求是否成功
  if (!validateHttpStatus(response.status)) {
    return {}
  }

  const result = response.data?.route

  // 如果 result 不存在，返回空对象
  if (!result) {
    return {}
  }

  // 处理重定向 redirect_code
  if (result && result?.redirect_code !== 0) {
    return {}
  }

  if (result?.type) {
    // Category
    if (result.__typename === 'CategoryTree') {
      return {
        title: {
          absolute: result?.meta_title || result.name!,
        },
        keywords: result?.meta_keywords || result.name,
        description: result?.meta_description || result.name,
        openGraph: {
          url: fullUrl,
          title: result?.meta_title || result.name!,
          description: result?.meta_description || result.name!,
          images: result.category_image ? [result.category_image] : [],
          locale: locale,
          type: 'website',
        },
        alternates: {
          canonical: fullUrl,
        },
      }
    }

    // Cms Page
    if (result.__typename === 'CmsPage') {
      return {
        title: {
          absolute: result?.meta_title || result.title!,
        },
        keywords: result?.meta_keywords || result.title,
        description: result?.meta_description || result.title,
        openGraph: {
          url: fullUrl,
          title: result?.meta_title || result.title!,
          description: result?.meta_description || result.title!,
          locale: locale,
          type: 'website',
        },
        alternates: {
          canonical: fullUrl,
        },
      }
    }

    // Product
    // TODO: 暂时无法提取这里的类型断言
    if (
      result.__typename === 'SimpleProduct' ||
      result.__typename === 'ConfigurableProduct' ||
      result.__typename === 'GroupedProduct' ||
      result.__typename === 'VirtualProduct' ||
      result.__typename === 'DownloadableProduct' ||
      result.__typename === 'BundleProduct'
    ) {
      return {
        title: {
          absolute: result?.meta_title || result.name!,
        },
        keywords: result?.meta_keyword || result.name,
        description: result?.meta_description || result.name,
        openGraph: {
          url: fullUrl,
          title: result?.meta_title || result.name!,
          description: result?.meta_description || result.name!,
          images: result.image?.url ? [result.image?.url] : [],
          locale: locale,
          type: 'website',
        },
        alternates: {
          canonical: fullUrl,
        },
      }
    }
  }

  return {}
}

/**
 * 路由匹配页面，承载如下页面类型：
 * 1.'CATEGORY'
 * 2.'CMS_PAGE'
 * 3.'PRODUCT'
 */
const Page = async ({ params }: TResolveUrlPageProps) => {
  const { locale, url: segments } = params

  if (!segments.length) {
    return notFound()
  }

  const headersList = headers()
  // TODO: 检查传入的 URL 安全性
  const safeUrl = decodeURIComponent(segments.join('/'))
  const quid = headersList.get(QUID_KEY.toUpperCase()) || ''

  const response = await resolveUrlQuery(
    {
      url: safeUrl,
    },
    {
      headers: {
        [QUID_KEY.toUpperCase()]: quid,
      },
    },
  )

  // 限流后跳转到限流页面
  if (response.isError && response.errorType === RATE_LIMIT) {
    return redirect({ href: `/${locale}/limit`, locale: locale })
  }

  // 检查请求是否成功
  if (!validateHttpStatus(response.status)) {
    return notFound()
  }
  const result = response.data?.route

  // 如果 result 不存在，返回 404
  if (!result) {
    return notFound()
  }

  if (result && result?.redirect_code !== 0) {
    // 处理重定向 redirect_code
    return redirect({ href: `/${locale}/${result?.relative_url}`, locale: locale })
  }

  // 处理页面类型
  if (result?.type) {
    // Category
    if (result.__typename === 'CategoryTree') {
      const response = await getCategoriesQuery(
        {
          filters: {
            category_uid: {
              eq: result.uid,
            },
          },
        },
        {
          headers: {
            [QUID_KEY.toUpperCase()]: quid,
          },
        },
      )

      // 限流后跳转到限流页面
      if (response.isError && response.errorType === RATE_LIMIT) {
        return redirect({ href: `/${locale}/limit`, locale: locale })
      }

      if (validateHttpStatus(response.status)) {
        if (response.data?.categories?.items?.length) {
          return <CategoryPage data={response.data.categories.items[0]!} />
        } else {
          return (
            <div className="mb-[205px] mt-32 flex flex-col items-center px-11 text-center xl:mb-44">
              <EmptyData
                isArrowBtnVisible={false}
                btnStyle="bg-[#f3f3f4] p-[20px]"
                pathname={segments.join('/')}
              />
            </div>
          )
        }
      }
      return notFound()
    }

    // Cms Page
    if (result.__typename === 'CmsPage') {
      const response = await getCmsPageQuery(
        {
          identifier: extractFileName(result.relative_url!),
        },
        {
          headers: {
            [QUID_KEY.toUpperCase()]: quid,
          },
        },
      )

      // 限流后跳转到限流页面
      if (response.isError && response.errorType === RATE_LIMIT) {
        return redirect({ href: `/${locale}/limit`, locale: locale })
      }

      if (validateHttpStatus(response.status) && response.data) {
        if (response.data) {
          return <CmsPage data={response.data} />
        } else {
          return (
            <div className="mb-[205px] mt-32 flex flex-col items-center px-11 text-center xl:mb-44">
              <EmptyData isArrowBtnVisible={false} btnStyle="bg-[#f3f3f4] p-[20px]" />
            </div>
          )
        }
      }
      return notFound()
    }

    // Product
    // TODO: 暂时无法提取这里的类型断言
    if (
      result.__typename === 'SimpleProduct' ||
      result.__typename === 'ConfigurableProduct' ||
      result.__typename === 'GroupedProduct' ||
      result.__typename === 'VirtualProduct' ||
      result.__typename === 'DownloadableProduct' ||
      result.__typename === 'BundleProduct'
    ) {
      const response = await getProductsQuery(
        {
          filter: {
            sku: {
              eq: result.sku,
            },
          },
        },
        {
          headers: {
            [QUID_KEY.toUpperCase()]: quid,
          },
        },
      )

      // 限流后跳转到限流页面
      if (response.isError && response.errorType === RATE_LIMIT) {
        return redirect({ href: `/${locale}/limit`, locale: locale })
      }

      if (validateHttpStatus(response.status)) {
        if (response.data?.products?.items?.length) {
          return <ProductPage data={response.data.products.items[0]!} />
        } else {
          return (
            <div className="mb-[205px] mt-32 flex flex-col items-center px-11 text-center xl:mb-44">
              <EmptyData
                isArrowBtnVisible={false}
                btnStyle="bg-[#f3f3f4] p-[20px]"
                message="商品已下架"
              />
            </div>
          )
        }
      }
      return notFound()
    }
  }

  notFound()
}
export default Page
