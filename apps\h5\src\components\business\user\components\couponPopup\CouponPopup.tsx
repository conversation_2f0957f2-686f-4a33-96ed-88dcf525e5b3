'use client'

import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslations } from 'next-intl'
import { IconUsed, OrderPickupInfo, OrderVouchers } from '@ninebot/core'
import { Image, Swiper } from 'antd-mobile'
import { SwiperRef } from 'antd-mobile/es/components/swiper'

import { CustomPopup } from '@/components'

type CouponPopupProps = {
  /** 是否显示弹窗 */
  popupVisible: boolean
  /** 拷贝到剪贴板的回调函数 */
  copyToClipboard: (text: string) => void
  /** 关闭弹窗的回调函数 */
  closePopup: () => void
  /** 自提取货码信息 */
  pickupInfo?: OrderPickupInfo
  /** 券码列表 */
  couponList?: OrderVouchers
}

type CarouselItem = {
  image_url: string | null | undefined
  status?: string | null
  status_label?: string | null
  imageStyle?: {
    opacity?: number
  }
}

const BgIcon = ({ color = '#FEE5E5' }: { color: string }) => (
  <svg width="74" height="98" viewBox="0 0 74 98" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      opacity="0.2"
      d="M52.0254 26.9336C53.7186 26.9303 60.4082 26.9177 64.3984 32.0449C67.4578 35.9531 67.7722 41.1597 65.6279 47.8555V47.8691C65.6279 47.8691 55.3839 80.6782 53.3486 87.1152C50.4531 96.2572 40.3871 100.399 31.9736 96.5859C23.4784 92.7187 24.9402 84.7246 25.6777 82.374L30.0479 68.3262C31.2225 64.5136 31.5774 62.3542 29.8428 59.7031C27.9718 57.0515 24.5931 57.0709 23.1514 57.0791H17.9062C15.9804 57.0791 14.4229 55.5215 14.4229 53.5947C14.4229 51.668 15.9805 50.0967 17.9062 50.0967H38.4746C43.2002 50.0967 45.5091 52.7202 43.2969 57.0791L32.2607 78.917C30.1303 83.0848 30.0617 88.291 34.7871 90.2861C40.6463 92.7595 45.6041 88.496 47.3525 82.8662C48.1716 80.2303 58.9639 45.7747 58.9756 45.7373C59.9316 42.7448 60.697 39.2193 58.8125 36.541C57.0507 34.0404 53.1445 33.9307 51.8467 33.9307H12.9756C11.0499 33.9307 9.49231 32.373 9.49219 30.4326C9.49219 28.4922 11.0498 26.9346 12.9756 26.9346H51.915C51.949 26.9346 51.9859 26.9337 52.0254 26.9336ZM47.0254 38.5361C48.9511 38.5363 50.5215 40.1075 50.5215 42.0342C50.5215 43.9609 48.9647 45.5184 47.0254 45.5186H3.49609C1.57048 45.5183 1.86569e-05 43.9608 0 42.0342C0 40.0939 1.57047 38.5363 3.49609 38.5361H47.0254ZM62.2676 0C68.7415 0 73.9999 5.24737 74 11.7383C74 18.2293 68.7415 23.4766 62.2676 23.4766C55.7936 23.4765 50.5352 18.2156 50.5352 11.7383C50.5352 5.26105 55.7937 2.48008e-05 62.2676 0Z"
      fill={color}
    />
  </svg>
)

/**
 * 券码/取货码弹窗组件
 * 支持两种模式：
 * 1. 取货码模式：显示单个取货码及其二维码
 * 2. 券码模式：显示多个券码及其二维码，支持轮播切换
 */
const CouponPopup = (props: CouponPopupProps) => {
  const { popupVisible, copyToClipboard, closePopup, pickupInfo, couponList = [] } = props
  const getI18nString = useTranslations('Common')
  const swiperRef = useRef<SwiperRef>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  /**
   * 判断当前是否为取货码模式
   */
  const isPickupCode = useMemo(() => {
    return !!pickupInfo
  }, [pickupInfo])

  /**
   * 构建轮播图数据
   * 取货码模式：返回单个取货码的二维码数据
   * 券码模式：返回券码列表的二维码数据
   */
  const carouselData = useMemo(() => {
    if (isPickupCode && pickupInfo) {
      return [
        {
          image_url: pickupInfo.qr_code,
          status: pickupInfo.status,
          status_label: pickupInfo.status_label,
          imageStyle: pickupInfo.status === '0' ? {} : { opacity: 0.08 },
        },
      ]
    }

    return couponList
      ?.map((coupon) => {
        if (!coupon) return null
        return {
          image_url: coupon.qr_code,
          status: coupon.status,
          status_label: coupon.status_label,
          imageStyle: coupon.status === '0' ? {} : { opacity: 0.08 },
        }
      })
      .filter(Boolean) as CarouselItem[]
  }, [isPickupCode, pickupInfo, couponList])

  /**
   * 重置轮播图状态
   */
  useEffect(() => {
    if (!popupVisible) {
      swiperRef.current?.swipeTo(0)
      setCurrentIndex(0)
    }
  }, [popupVisible])

  /**
   * 渲染券码卡片主体
   */
  const renderCouponCard = (itemData: CarouselItem, index: number) => {
    const code = isPickupCode ? pickupInfo?.code : couponList?.[index]?.code
    const isItemUnUsed = itemData?.status === '0'

    return (
      <div className="mx-auto w-[312px] rounded-[12px] bg-white shadow-sm">
        {/* 券码信息区域 */}
        <div className="relative pb-[23px] pl-[16px] pr-[12px] pt-[17px]">
          {/* 左右两侧的圆形装饰 */}
          <div className="absolute -bottom-[6px] -left-[5px] h-[10px] w-[10px] rounded-full bg-[#F5F5F7]" />
          <div className="absolute -bottom-[6px] -right-[5px] h-[10px] w-[10px] rounded-full bg-[#F5F5F7]" />

          <div className="flex items-center">
            {/* 券码序号显示 */}
            <div className="min-w-[39px] pr-[2px] text-center">
              <div
                className={`font-miSansRegular380 text-[16px] leading-[22px] ${
                  isItemUnUsed ? 'text-[#000000]' : 'text-[#86868B]'
                }`}>
                {getI18nString('verification_code_index', { key: index + 1 })}
              </div>
            </div>

            {/* 虚线分割 */}
            <div className="mx-base-12 h-[62px] w-[1px] border-l border-dashed border-[#E1E1E4]" />

            {/* 券码详情区域 */}
            <div className="flex flex-1 flex-col">
              {/* 券码文本 */}
              <div
                className={`font-miSansDemiBold450 text-[16px] leading-[21px] ${
                  isItemUnUsed ? 'text-[#000000]' : 'text-[#86868B]'
                }`}>
                {code}
              </div>

              {/* 过期时间 */}
              <div className="font-miSansRegular380 mt-1 text-[12px] leading-[14px] text-[#86868B]">
                {isPickupCode
                  ? pickupInfo?.expired_info
                  : getI18nString('expired_time_until') +
                    couponList?.[index]?.expired_at?.split(' ')[0]}
              </div>
            </div>

            {/* 复制按钮（仅未使用状态显示） */}
            {isItemUnUsed && (
              <button
                className="z-10 ml-2 flex h-[32px] items-center justify-center rounded-full border border-primary px-base-12"
                onClick={() => copyToClipboard(code || '')}>
                <div className="font-miSansRegular380 text-[13px] leading-[16px] text-primary">
                  {getI18nString('copy')}
                </div>
              </button>
            )}
          </div>

          <div className="absolute right-[27px] top-[12px]">
            <BgIcon color={isItemUnUsed ? '#FEE5E5' : '#F3F3F4'} />
          </div>
        </div>

        {/* 虚线分割 */}
        <div className="mx-4 h-[1px] border-b border-dashed border-[#E1E1E4]" />

        {/* 二维码区域 */}
        <div className="flex justify-center px-6 py-6">
          <div className="relative h-[180px] w-[180px]">
            <Image
              className={`h-full w-full ${itemData?.status !== '0' ? 'opacity-[0.08]' : ''}`}
              src={itemData?.image_url || ''}
              fit="cover"
              alt="券码二维码"
            />
            {itemData?.status !== '0' && (
              <div className="absolute bottom-[8px] right-[8px]">
                <IconUsed />
                <span className="absolute bottom-[24px] right-[8px] -rotate-[40deg] text-[20px] text-[#86868B]">
                  {itemData?.status_label}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  /**
   * 渲染轮播控制按钮区域
   */
  const renderCarouselControls = () => (
    <div className="flex flex-col items-center">
      {/* 券码卡片轮播 */}
      <div className="w-full">
        <Swiper
          ref={swiperRef}
          loop={false}
          indicator={() => null}
          onIndexChange={(index) => setCurrentIndex(index)}>
          {carouselData
            ?.filter(Boolean)
            .map((item, index) => (
              <Swiper.Item key={index}>{renderCouponCard(item, index)}</Swiper.Item>
            ))}
        </Swiper>
      </div>

      {/* 轮播指示器 */}
      <div className="my-base flex h-[30px] items-center justify-center gap-base">
        {carouselData &&
          carouselData.length > 1 &&
          carouselData.map((_, index) => (
            <div
              key={index}
              className={`h-[6px] rounded-full ${
                index === currentIndex ? 'w-[28px] bg-[#000000D9]' : 'w-[6px] bg-[#00000066]'
              }`}
            />
          ))}
      </div>
    </div>
  )

  return (
    <CustomPopup
      visible={popupVisible}
      onClose={closePopup}
      showHeader
      headerClassName="border-none"
      bodyStyle={{ background: '#F3F3F4' }}
      headTitle={getI18nString('coupon_code')}>
      <div className="mt-4 px-base-12">
        {/* 券码卡片和轮播控制区域 */}
        {renderCarouselControls()}
      </div>
    </CustomPopup>
  )
}

export default CouponPopup
